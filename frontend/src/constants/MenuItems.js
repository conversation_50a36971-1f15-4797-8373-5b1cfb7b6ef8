import { PrimeIcons } from 'primereact/api';
import UrlRouter from './UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import { checkUserContextIsInspetor, checkuserContextIsAuditor, checkUserGroup } from 'fc/utils/utils';

const menus = [
  {
    items: [
      {
        label: 'Catálogo de Produtos',
        icon: PrimeIcons.TABLE,
        to: UrlRouter.catalogo.consultaMateriais.index,
        accessPermission: AccessPermission.consultaMateriais,
      },
      {
        label: 'Termo de Referência',
        icon: PrimeIcons.FILE,
        router: UrlRouter.termoReferencia.default,
        items: [
          {
            label: 'Seções',
            icon: PrimeIcons.BOOKMARK,
            to: UrlRouter.termoReferencia.secao.index,
            accessPermission: AccessPermission.secoes,
          },
          {
            label: 'Gerenciamento de Termos',
            icon: PrimeIcons.FILE,
            to: UrlRouter.termoReferencia.gerenciamentoTermos.index,
            accessPermission: AccessPermission.gerenciamentoTermo,
          },
        ],
      },
      {
        label: 'Cadastros e Consultas',
        icon: PrimeIcons.GLOBE,
        router: UrlRouter.cadastrosConsulta.default,
        items: [
          {
            label: 'Licitação',
            icon: PrimeIcons.BRIEFCASE,
            to: UrlRouter.cadastrosConsulta.licitacao.index,
            accessPermission: AccessPermission.boardLicitacoes,
          },
          {
            label: 'Adesão/Carona',
            icon: PrimeIcons.DOLLAR,
            to: UrlRouter.cadastrosConsulta.carona.index,
            accessPermission: AccessPermission.carona,
          },
          {
            label: 'Dispensa',
            icon: PrimeIcons.BOOK,
            to: UrlRouter.cadastrosConsulta.dispensa.index,
            accessPermission: AccessPermission.dispensa,
          },
          {
            label: 'Inexigibilidade',
            icon: PrimeIcons.COMMENT,
            to: UrlRouter.cadastrosConsulta.inexigibilidade.index,
            accessPermission: AccessPermission.inexigibilidade,
          },
          {
            label: 'Credenciamento',
            icon: PrimeIcons.CHECK_SQUARE,
            to: UrlRouter.cadastrosConsulta.credenciamento.index,
            accessPermission: AccessPermission.credenciamento,
          },
          {
            label: 'Contrato/Aditivo',
            icon: PrimeIcons.BOOK,
            to: UrlRouter.cadastrosConsulta.contrato.index,
            accessPermission: AccessPermission.contrato,
          },
          {
            label: 'Consultar Processos',
            icon: PrimeIcons.SEARCH,
            to: UrlRouter.cadastrosConsulta.consultaProcesso.index,
            accessPermission: AccessPermission.consultaProcessos,
          },
        ],
      },
      {
        label: 'Auditoria',
        icon: PrimeIcons.CLONE,
        router: UrlRouter.auditoria.default,
        items: [
          {
            label: 'Alertas Dafo',
            icon: PrimeIcons.BELL,
            to: UrlRouter.auditoria.analisarAlertas.index,
            accessPermission: AccessPermission.analisarAlertaDafo,
          },
          {
            label: 'Alertas Inspetor',
            icon: PrimeIcons.BELL,
            to: UrlRouter.auditoria.alertasInspetor.index,
            accessPermission: AccessPermission.alerta,
            visible: checkUserContextIsInspetor,
          },
          {
            label: 'Analisar Processos',
            icon: PrimeIcons.SEARCH_MINUS,
            to: UrlRouter.auditoria.analiseProcesso.index,
            accessPermission: AccessPermission.analiseProcessoView,
          },
          {
            label: 'Seções Checklist',
            icon: PrimeIcons.CHECK_SQUARE,
            to: UrlRouter.auditoria.secaoChecklist.index,
            accessPermission: AccessPermission.secaoChecklist,
          },
          {
            label: 'Checklist de Verificação',
            icon: PrimeIcons.CHECK_SQUARE,
            to: UrlRouter.auditoria.itemChecklist.index,
            accessPermission: AccessPermission.itemChecklist,
          },
          {
            label: 'Requisições de Modificação',
            icon: PrimeIcons.EXCLAMATION_TRIANGLE,
            to: UrlRouter.auditoria.requisicaoModificacao.index,
            accessPermission: ['avaliar_requisicao_modificacao'],
          },
          {
            label: 'Análise Automática',
            icon: PrimeIcons.SEARCH_MINUS,
            to: UrlRouter.auditoria.analiseAutomatica.index,
            accessPermission: AccessPermission.paramAnaliseAutomatica,
          },
          {
            label: 'Histórico de Processos',
            icon: PrimeIcons.FOLDER,
            to: UrlRouter.auditoria.historicoProcessos.index,
            accessPermission: AccessPermission.historicoProcessos,
          },
          {
            label: 'Análise de Editais',
            icon: PrimeIcons.SEARCH_MINUS,
            to: UrlRouter.auditoria.analisarEditais.index,
            accessPermission: AccessPermission.edital,
          },
          {
            label: 'Monitoramento Atos Diário Oficial',
            icon: PrimeIcons.DESKTOP,
            to: UrlRouter.auditoria.monitoramentoAtosDiarioOficial.index,
            accessPermission: AccessPermission.monitoramentoAtosDiarioOficialView,
          },
          {
            label: 'Mapeamento de Atos com Licitações',
            icon: PrimeIcons.DESKTOP,
            to: UrlRouter.auditoria.mapeamentoAtosLicitacoes.index,
            accessPermission: AccessPermission.mapeamentoAtosLicitacoes,
          },
          {
            label: 'Painel de Atos Vinculados com Licitações',
            icon: PrimeIcons.CHART_BAR,
            to: UrlRouter.auditoria.painelAtosLicitacoes.index,
            accessPermission: AccessPermission.painelAtosLicitacoes,
          },
          {
            label: 'Relatórios',
            icon: PrimeIcons.FILE_PDF,
            onClickAction: () => {
              const token = encodeURIComponent(localStorage.getItem('tokenAtual'));
              window.open(window._env_.RELATORIOS_FE_URL + `?token=${token}`, '_blank');
            },
            visible: () => {
              return checkuserContextIsAuditor() || checkUserGroup('Auditor - Demais Inspetorias');
            },
          },
        ],
      },
      {
        label: 'Alertas',
        icon: PrimeIcons.BELL,
        to: UrlRouter.alerta.index,
        accessPermission: AccessPermission.alerta,
      },
      {
        label: 'Administração',
        icon: PrimeIcons.MONEY_BILL,
        router: UrlRouter.administracao.default,
        items: [
          {
            label: 'Comissão',
            icon: PrimeIcons.USERS,
            to: UrlRouter.administracao.comissao.index,
            accessPermission: AccessPermission.comissao,
          },
          {
            label: 'Requisições de Modificação',
            icon: PrimeIcons.EXCLAMATION_TRIANGLE,
            to: UrlRouter.administracao.requisicaoModificacao.index,
            accessPermission: AccessPermission.requisicaoModificacao,
          },
          {
            label: 'Elementos de Despesa',
            icon: PrimeIcons.DOLLAR,
            to: UrlRouter.administracao.elementoDespesa.index,
            accessPermission: AccessPermission.elementoDespesa,
          },
          {
            label: 'Falhas',
            icon: PrimeIcons.BAN,
            to: UrlRouter.administracao.falha.index,
            accessPermission: AccessPermission.falha,
          },
          {
            label: 'Feriados',
            icon: PrimeIcons.CALENDAR,
            to: UrlRouter.administracao.feriado.index,
            accessPermission: AccessPermission.feriado,
          },
          {
            label: 'Fontes de Recurso',
            icon: PrimeIcons.PAPERCLIP,
            to: UrlRouter.administracao.fonteRecurso.index,
            accessPermission: AccessPermission.fonteRecurso,
          },
          {
            label: 'Formas de Licitação',
            icon: PrimeIcons.BOOK,
            to: UrlRouter.administracao.formaLicitacao.index,
            accessPermission: AccessPermission.formaLicitacao,
          },
          {
            label: 'Formas de Publicação',
            icon: PrimeIcons.LINK,
            to: UrlRouter.administracao.formaPublicacao.index,
            accessPermission: AccessPermission.formaPublicacao,
          },
          {
            label: 'Fundamentação Legal',
            icon: PrimeIcons.CHECK_SQUARE,
            to: UrlRouter.administracao.fundamentacaoLegal.index,
            accessPermission: AccessPermission.fundamentacaoLegal,
          },
          {
            label: 'Licitantes',
            icon: PrimeIcons.USER,
            to: UrlRouter.administracao.licitante.index,
            accessPermission: AccessPermission.licitante,
          },
          {
            label: 'Critérios de Julgamento',
            icon: PrimeIcons.TAGS,
            to: UrlRouter.administracao.tipoLicitacao.index,
            accessPermission: AccessPermission.tipoLicitacao,
          },
          {
            label: 'Modalidades de Licitação',
            icon: PrimeIcons.LIST,
            to: UrlRouter.administracao.modalidadeLicitacao.index,
            accessPermission: AccessPermission.modalidadeLicitacao,
          },
          {
            label: 'Gerador de Links',
            icon: PrimeIcons.LINK,
            to: UrlRouter.administracao.linkConsulta.index,
            accessPermission: AccessPermission.geradorLinks,
          },
          {
            label: 'Variáveis de Controle',
            icon: PrimeIcons.KEY,
            to: UrlRouter.administracao.variavelControle.index,
            accessPermission: AccessPermission.variavelControle,
          },
          {
            label: 'Parametrização dos Arquivos',
            icon: PrimeIcons.FILE,
            to: UrlRouter.administracao.obrigatoriedadeArquivo.index,
            accessPermission: AccessPermission.paramArquivo,
          },
          {
            label: 'Entidade Externa',
            icon: PrimeIcons.TH_LARGE,
            to: UrlRouter.administracao.entidadeExterna.index,
            accessPermission: AccessPermission.entidadeExterna,
          },
          {
            label: 'Responsável Ente',
            icon: PrimeIcons.ENVELOPE,
            to: UrlRouter.administracao.responsavelEnte.index,
            accessPermission: AccessPermission.responsavelEnte,
          },
          {
            label: 'Configurações de Email',
            icon: PrimeIcons.AT,
            to: UrlRouter.administracao.configuracoesEmail.editar,
            accessPermission: AccessPermission.configuracoesEmail,
          },
          {
            label: 'Gestor e Fiscal',
            icon: PrimeIcons.USER,
            to: UrlRouter.administracao.responsavelContrato.index,
            accessPermission: AccessPermission.responsavelContrato,
          },
        ],
      },
      {
        label: 'Segurança',
        icon: PrimeIcons.LOCK,
        router: UrlRouter.seguranca.default,
        items: [
          {
            label: 'Grupos',
            icon: PrimeIcons.USERS,
            to: UrlRouter.seguranca.grupoUsuario.index,
            accessPermission: AccessPermission.grupoUsuario,
          },
          {
            label: 'Usuários',
            icon: PrimeIcons.USER,
            to: UrlRouter.seguranca.usuario.index,
            accessPermission: AccessPermission.usuario,
          },
          {
            label: 'Funções de Risco',
            icon: PrimeIcons.EXCLAMATION_TRIANGLE,
            to: UrlRouter.seguranca.funcaoRisco.index,
            accessPermission: AccessPermission.funcaoRisco,
          },
          {
            label: 'Configurações',
            icon: PrimeIcons.COG,
            to: UrlRouter.seguranca.configuracoes.editar,
            accessPermission: AccessPermission.configuracoes,
          },
        ],
      },
      {
        label: 'Obras',
        icon: PrimeIcons.MAP,
        router: UrlRouter.obra.default,
        items: [
          {
            label: 'Tipos de Obras',
            icon: PrimeIcons.TAGS,
            to: UrlRouter.obra.tipoObra.index,
            accessPermission: AccessPermission.geoObras.tipoObra,
          },
          {
            label: 'Cadastro de Obras',
            icon: PrimeIcons.BUILDING,
            to: UrlRouter.obra.cadastro.index,
            accessPermission: AccessPermission.geoObras.obra,
          },
          {
            label: 'Acompanhamento',
            icon: PrimeIcons.CALENDAR_TIMES,
            to: UrlRouter.obra.acompanhamento.index,
            accessPermission: AccessPermission.geoObras.acompanhamento,
          },
          {
            label: 'Mapa de Obras',
            icon: PrimeIcons.MAP_MARKER,
            to: UrlRouter.obra.mapa.index,
            accessPermission: AccessPermission.geoObras.obra,
          },
          {
            label: 'SICRO/SINAPI',
            icon: PrimeIcons.DOLLAR,
            to: UrlRouter.obra.relatorioObra.index,
            accessPermission: AccessPermission.relatorioObra,
          },
        ],
      },
    ],
  },
];

export default menus;
