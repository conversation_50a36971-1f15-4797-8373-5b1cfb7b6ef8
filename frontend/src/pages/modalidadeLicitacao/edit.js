import React from 'react';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import ModalidadeLicitacaoFormPage from './form';

const EditModalidadeLicitacao = (props) => {
  return <ModalidadeLicitacaoFormPage action="edit" id={props.match.params.id} {...props} />;
};

EditModalidadeLicitacao.propTypes = {
  match: PropTypes.shape({
    params: PropTypes.shape({
      id: PropTypes.string.isRequired,
    }).isRequired,
  }).isRequired,
};

export default withRouter(EditModalidadeLicitacao);
