import React from 'react';
import { observer } from 'mobx-react';
import ModalidadeLicitacaoIndexStore from '../../stores/modalidadeLicitacao/indexStore';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '../../constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { getMultipleValuesByKey, getValueDate } from 'fc/utils/utils';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import Tooltip from 'fc/components/Tooltip';

@observer
class ModalidadeLicitacaoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.modalidadeLicitacao);
    this.store = new ModalidadeLicitacaoIndexStore();
    this.state = {
      idRemove: null,
    };
  }

  render() {
    const columns = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
        style: { width: '30%' },
      },
      {
        field: 'vigenciaDe',
        header: 'Vigência De',
        sortable: true,
        body: ({ vigenciaDe }) => getValueDate(vigenciaDe, DATE_FORMAT, DATE_PARSE_FORMAT),
        style: { width: '15%' },
      },
      {
        field: 'vigenciaAte',
        header: 'Vigência Até',
        sortable: true,
        body: ({ vigenciaAte }) => getValueDate(vigenciaAte, DATE_FORMAT, DATE_PARSE_FORMAT),
        style: { width: '15%' },
      },
      {
        field: 'permiteConsorcio',
        header: 'Permite Consórcio',
        sortable: true,
        body: ({ permiteConsorcio }) => (permiteConsorcio ? 'Sim' : 'Não'),
        style: { width: '15%' },
      },
      {
        field: 'legislacao',
        header: 'Legislação',
        sortable: true,
        body: ({ legislacao }) =>
          getMultipleValuesByKey(legislacao, DadosEstaticosService.getTipoLicitacaoLegislacao()),
        style: { width: '15%' },
      },
      {
        field: 'acoes',
        header: 'Ações',
        body: (rowData) => (
          <div className="p-d-flex">
            <PermissionProxy resourcePermissions={AccessPermission.modalidadeLicitacao.writePermission}>
              <Tooltip target=".btn-edit" content="Editar" position="top" />
              <FcButton
                className="p-button-rounded p-button-success p-mr-2 btn-edit"
                icon={PrimeIcons.PENCIL}
                onClick={() =>
                  this.props.history.push(UrlRouter.administracao.modalidadeLicitacao.editar.replace(':id', rowData.id))
                }
              />
            </PermissionProxy>
            <PermissionProxy resourcePermissions={AccessPermission.modalidadeLicitacao.writePermission}>
              <Tooltip target=".btn-delete" content="Excluir" position="top" />
              <FcButton
                className="p-button-rounded p-button-danger btn-delete"
                icon={PrimeIcons.TRASH}
                onClick={() => this.confirmRemove(rowData.id)}
              />
            </PermissionProxy>
          </div>
        ),
        style: { width: '10%' },
      },
    ];

    const breadcrumbItems = [{ label: 'Modalidades de Licitação' }];

    return (
      <div className="datatable-crud-demo">
        <div className="card">
          <AppBreadCrumb items={breadcrumbItems} />

          <div className="p-d-flex p-jc-between p-ai-center p-mb-4">
            <h2>Modalidades de Licitação</h2>
            <PermissionProxy resourcePermissions={AccessPermission.modalidadeLicitacao.writePermission}>
              <FcButton
                label="Nova Modalidade"
                icon={PrimeIcons.PLUS}
                className="p-button-success p-mr-2"
                onClick={() => this.props.history.push(UrlRouter.administracao.modalidadeLicitacao.novo)}
              />
            </PermissionProxy>
          </div>

          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['nome']}
          />

          <IndexDataTable
            store={this.store}
            columns={columns}
            confirmDialog={this.state.confirmDialog}
            onHideDialog={() => this.setState({ confirmDialog: false })}
            onConfirmRemove={() => this.remove()}
          />
        </div>
      </div>
    );
  }
}

export default ModalidadeLicitacaoIndexPage;
