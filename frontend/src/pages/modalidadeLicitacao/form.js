import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import ModalidadeLicitacaoFormStore from '~/stores/modalidadeLicitacao/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import <PERSON>Field from 'fc/components/FormField';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FcMultiSelect from 'fc/components/FcMultiSelect';
import FcCalendar from 'fc/components/FcCalendar';
import { Checkbox } from 'primereact/checkbox';

@observer
class ModalidadeLicitacaoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.modalidadeLicitacao.index, AccessPermission.modalidadeLicitacao);
    this.store = new ModalidadeLicitacaoFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { permiteConsorcio: false });
  }

  render() {
    const { submitted } = this.state;
    const { getRule, updateAttribute, updateAttributeDateWithHours, updateAttributeCheckbox } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Modalidades de Licitação', url: UrlRouter.administracao.modalidadeLicitacao.index },
      { label: this.props.action === 'new' ? 'Nova' : 'Editar' },
    ];

    let content;

    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={12}
                  attribute="nome"
                  label="Nome"
                  rule={getRule('nome')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('nome', e)}
                    placeholder="Informe o nome da modalidade"
                    value={this.store.object.nome || ''}
                    id="nome"
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="vigenciaDe"
                  label="Vigência De"
                  rule={getRule('vigenciaDe')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={this.getDateAttributeValue(this.store.object.vigenciaDe)}
                    onChange={(e) => updateAttributeDateWithHours('vigenciaDe', e)}
                    placeholder="Selecione a data de vigência inicial"
                    showTime
                    showSeconds
                    id="vigenciaDe"
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="vigenciaAte"
                  label="Vigência Até"
                  rule={getRule('vigenciaAte')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={this.getDateAttributeValue(this.store.object.vigenciaAte)}
                    onChange={(e) => updateAttributeDateWithHours('vigenciaAte', e)}
                    placeholder="Selecione a data de vigência final"
                    showTime
                    showSeconds
                    id="vigenciaAte"
                  />
                </FormField>

                <FormField
                  columns={12}
                  attribute="legislacao"
                  label="Legislação"
                  rule={getRule('legislacao')}
                  submitted={submitted}
                >
                  <FcMultiSelect
                    id="legislacao"
                    value={this.store.object.legislacao}
                    options={DadosEstaticosService.getTipoLicitacaoLegislacao()}
                    onChange={(e) => updateAttribute('legislacao', e)}
                    placeholder="Selecione as legislações"
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="permiteConsorcio"
                  label="Permite Consórcio"
                  checkbox
                  submitted={submitted}
                >
                  <Checkbox
                    inputId="permiteConsorcio"
                    checked={this.store.object.permiteConsorcio || false}
                    onChange={(e) => updateAttributeCheckbox('permiteConsorcio', e)}
                    id="permiteConsorcio"
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

ModalidadeLicitacaoFormPage.propTypes = {
  action: PropTypes.string.isRequired,
  id: PropTypes.string,
};

export default ModalidadeLicitacaoFormPage;
