import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import ModalidadeLicitacaoFormStore from '~/stores/modalidadeLicitacao/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import FormField from 'fc/components/FormField';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FcMultiSelect from 'fc/components/FcMultiSelect';
import FcCalendar from 'fc/components/FcCalendar';
import { Checkbox } from 'primereact/checkbox';

@observer
class ModalidadeLicitacaoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.modalidadeLicitacao.index, AccessPermission.modalidadeLicitacao);
    this.store = new ModalidadeLicitacaoFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { permiteConsorcio: false });
  }

  render() {
    const { submitted } = this.state;
    const { getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Modalidades de Licitação', url: UrlRouter.administracao.modalidadeLicitacao.index },
      { label: this.props.action === 'new' ? 'Nova' : 'Editar' },
    ];

    if (!this.store.object) {
      return <div>Carregando...</div>;
    }

    return (
      <div className="p-fluid">
        <div className="card">
          <AppBreadCrumb items={breacrumbItems} />
          <h2>{this.props.action === 'new' ? 'Nova Modalidade de Licitação' : 'Editar Modalidade de Licitação'}</h2>

          <form onSubmit={submitFormData} className="p-fluid">
            <div className="p-field">
              <FormField
                htmlFor="nome"
                label="Nome"
                attribute="nome"
                required
                error={getRule('nome', submitted)}
                component={
                  <InputText
                    id="nome"
                    value={this.store.object.nome || ''}
                    onChange={(e) => this.store.updateAttribute('nome', e)}
                    className={getRule('nome', submitted) ? 'p-invalid' : ''}
                  />
                }
              />
            </div>

            <div className="p-formgrid p-grid">
              <div className="p-field p-col-12 p-md-6">
                <FormField
                  htmlFor="vigenciaDe"
                  label="Vigência De"
                  required
                  error={getRule('vigenciaDe', submitted)}
                  component={
                    <FcCalendar
                      id="vigenciaDe"
                      value={this.getDateAttributeValue(this.store.object.vigenciaDe)}
                      onChange={(e) => this.store.updateAttributeDateWithHours('vigenciaDe', e)}
                      showTime
                      showSeconds
                      className={getRule('vigenciaDe', submitted) ? 'p-invalid' : ''}
                    />
                  }
                />
              </div>

              <div className="p-field p-col-12 p-md-6">
                <FormField
                  htmlFor="vigenciaAte"
                  label="Vigência Até"
                  error={getRule('vigenciaAte', submitted)}
                  component={
                    <FcCalendar
                      id="vigenciaAte"
                      value={this.getDateAttributeValue(this.store.object.vigenciaAte)}
                      onChange={(e) => this.store.updateAttributeDateWithHours('vigenciaAte', e)}
                      showTime
                      showSeconds
                      className={getRule('vigenciaAte', submitted) ? 'p-invalid' : ''}
                    />
                  }
                />
              </div>
            </div>

            <div className="p-field">
              <FormField
                htmlFor="legislacao"
                label="Legislação"
                error={getRule('legislacao', submitted)}
                component={
                  <FcMultiSelect
                    id="legislacao"
                    value={this.store.object.legislacao}
                    options={DadosEstaticosService.getTipoLicitacaoLegislacao()}
                    onChange={(e) => this.store.updateAttribute('legislacao', e)}
                    placeholder="Selecione as legislações"
                    className={getRule('legislacao', submitted) ? 'p-invalid' : ''}
                  />
                }
              />
            </div>

            <div className="p-field">
              <FormField
                htmlFor="permiteConsorcio"
                label="Permite Consórcio"
                error={getRule('permiteConsorcio', submitted)}
                component={
                  <Checkbox
                    id="permiteConsorcio"
                    checked={this.store.object.permiteConsorcio || false}
                    onChange={(e) => this.store.updateAttributeCheckbox('permiteConsorcio', e)}
                  />
                }
              />
            </div>

            <PermissionProxy resourcePermissions={AccessPermission.modalidadeLicitacao.writePermission}>
              {this.getFormButtons()}
            </PermissionProxy>
          </form>
        </div>
      </div>
    );
  }
}

ModalidadeLicitacaoFormPage.propTypes = {
  action: PropTypes.string.isRequired,
  id: PropTypes.string,
};

export default ModalidadeLicitacaoFormPage;
