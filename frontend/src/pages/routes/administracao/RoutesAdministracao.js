import { Switch, Route } from 'react-router';
import UrlRouter from '~/constants/UrlRouter';
import RoutesElementoDespesa from './RoutesElementoDespesa';
import RoutesFalha from './RoutesFalha';
import RoutesFeriado from './RoutesFeriado';
import RoutesFonteRecurso from './RoutesFonteRecurso';
import RoutesFormaLicitacao from './RoutesFormaLicitacao';
import RoutesFormaPublicacao from './RoutesFormaPublicacao';
import RoutesLicitante from './RoutesLicitante';
import RoutesTipoLicitacao from './RoutesTipoLicitacao';
import RoutesModalidadeLicitacao from './RoutesModalidadeLicitacao';
import RoutesLinkConsulta from './RoutesLinkConsulta';
import RoutesVariavelControle from './RoutesVariavelControle';
import NotFound from 'fc/pages/NotFound';
import RoutesParametrizacaoArquivos from './RoutesParametrizacaoArquivos';
import RoutesEntidadeExterna from './RoutesEntidadeExterna';
import RoutesFundamentacaoLegal from './RoutesFundamentacaoLegal';
import RoutesResponsavelEnte from './RoutesResponsavelEnte';
import RoutesConfiguracoesEmail from './RoutesConfiguracoesEmail';
import RoutesRequisicaoModificacao from '../cadastrosConsulta/RoutesRequisicaoModificacao';
import RoutesComissao from './RoutesComissao';
import RoutesResponsavelContrato from '../administracao/RoutesResponsavelContrato';

const RoutesAdministracao = () => {
  return (
    <Switch>
      <Route path={UrlRouter.administracao.elementoDespesa.index} component={RoutesElementoDespesa} />
      <Route path={UrlRouter.administracao.entidadeExterna.index} component={RoutesEntidadeExterna} />
      <Route path={UrlRouter.administracao.falha.index} component={RoutesFalha} />
      <Route path={UrlRouter.administracao.feriado.index} component={RoutesFeriado} />
      <Route path={UrlRouter.administracao.fonteRecurso.index} component={RoutesFonteRecurso} />
      <Route path={UrlRouter.administracao.formaLicitacao.index} component={RoutesFormaLicitacao} />
      <Route path={UrlRouter.administracao.formaPublicacao.index} component={RoutesFormaPublicacao} />
      <Route path={UrlRouter.administracao.fundamentacaoLegal.index} component={RoutesFundamentacaoLegal} />
      <Route path={UrlRouter.administracao.licitante.index} component={RoutesLicitante} />
      <Route path={UrlRouter.administracao.tipoLicitacao.index} component={RoutesTipoLicitacao} />
      <Route path={UrlRouter.administracao.modalidadeLicitacao.index} component={RoutesModalidadeLicitacao} />
      <Route path={UrlRouter.administracao.linkConsulta.index} component={RoutesLinkConsulta} />
      <Route path={UrlRouter.administracao.variavelControle.index} component={RoutesVariavelControle} />
      <Route path={UrlRouter.administracao.obrigatoriedadeArquivo.index} component={RoutesParametrizacaoArquivos} />
      <Route path={UrlRouter.administracao.responsavelEnte.index} component={RoutesResponsavelEnte} />
      <Route path={UrlRouter.administracao.configuracoesEmail.editar} component={RoutesConfiguracoesEmail} />
      <Route path={UrlRouter.administracao.requisicaoModificacao.index} component={RoutesRequisicaoModificacao} />
      <Route path={UrlRouter.administracao.comissao.index} component={RoutesComissao} />
      <Route path={UrlRouter.administracao.responsavelContrato.index} component={RoutesResponsavelContrato} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesAdministracao;
